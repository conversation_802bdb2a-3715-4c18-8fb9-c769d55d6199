import { renderHook } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock d3 completely to avoid execution issues
jest.mock('d3', () => ({
  scaleLinear: () => ({
    domain: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    clamp: jest.fn().mockReturnThis(),
    nice: jest.fn().mockReturnThis(),
  }),
  scaleBand: () => ({
    domain: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    padding: jest.fn().mockReturnThis(),
    bandwidth: jest.fn(() => 30),
  }),
  scaleOrdinal: () => ({
    domain: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
  }),
}));

// Mock the actual hook to return predictable values
jest.mock('../../DashboardWidget/hooks/useD3Chart', () => ({
  useD3Chart: (config: any) => ({
    xScale: jest.fn((value) => value * 5),
    yScale: Object.assign(
      jest.fn((name) => config.vessels.findIndex((v: any) => (v.uniqueId || v.name) === name) * 40),
      { bandwidth: () => 30 }
    ),
    barColorScale: jest.fn((key) => key === 'Status A' ? '#ff0000' : '#00ff00'),
    textColorScale: jest.fn(() => '#ffffff'),
    stackedBarData: config.vessels.map((vessel: any, index: number) => ({
      vesselName: vessel.name,
      uniqueId: vessel.uniqueId || vessel.name,
      segments: config.valueHeaders.map((header: string) => ({
        key: header,
        value: vessel[header] || 0,
        x: 0,
        width: 30,
        y: index * 40,
        height: 30,
        vesselName: vessel.name,
        uniqueId: vessel.uniqueId || vessel.name,
      })).filter((segment: any) => segment.value > 0),
    })),
    chartHeight: config.vessels.length * config.heightPerBar,
    totalHeight: config.vessels.length * config.heightPerBar + config.margin.top + config.margin.bottom,
  }),
}));

const { useD3Chart } = require('../../DashboardWidget/hooks/useD3Chart');

const mockConfig = {
  vessels: [
    { name: 'Vessel A', uniqueId: 'Vessel A_0', 'Status A': 10, 'Status B': 5 },
    { name: 'Vessel B', uniqueId: 'Vessel B_1', 'Status A': 8, 'Status B': 12 },
    { name: 'Vessel A', uniqueId: 'Vessel A_2', 'Status A': 3, 'Status B': 7 },
  ],
  valueHeaders: ['Status A', 'Status B'],
  badgeColors: ['#ff0000', '#00ff00'],
  valueDomain: [0, 25] as [number, number],
  chartWidth: 800,
  heightPerBar: 40,
  margin: { top: 20, right: 20, bottom: 50, left: 150 },
};

describe('useD3Chart Hook', () => {
  test('returns correct chart dimensions', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    expect(result.current.chartHeight).toBe(120); // 3 vessels * 40px
    expect(result.current.totalHeight).toBe(190); // 120 + 20 + 50 (margins)
  });

  test('creates scales correctly', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    expect(result.current.xScale).toBeDefined();
    expect(result.current.yScale).toBeDefined();
    expect(result.current.barColorScale).toBeDefined();
    expect(result.current.textColorScale).toBeDefined();
  });

  test('processes stacked bar data with fixed width', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    const stackedBarData = result.current.stackedBarData;
    
    // Should have 3 vessels
    expect(stackedBarData).toHaveLength(3);
    
    // Each vessel should have segments with fixed 30px width
    stackedBarData.forEach((bar: any) => {
      bar.segments.forEach((segment: any) => {
        expect(segment.width).toBe(30); // Fixed width
        expect(segment.uniqueId).toBeDefined();
        expect(segment.vesselName).toBeDefined();
      });
    });
  });

  test('handles unique IDs correctly', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    const stackedBarData = result.current.stackedBarData;
    
    // Check that unique IDs are preserved
    expect(stackedBarData[0].uniqueId).toBe('Vessel A_0');
    expect(stackedBarData[1].uniqueId).toBe('Vessel B_1');
    expect(stackedBarData[2].uniqueId).toBe('Vessel A_2');
  });

  test('filters out zero values', () => {
    const configWithZeros = {
      ...mockConfig,
      vessels: [
        { name: 'Vessel A', uniqueId: 'Vessel A_0', 'Status A': 0, 'Status B': 5 },
        { name: 'Vessel B', uniqueId: 'Vessel B_1', 'Status A': 8, 'Status B': 0 },
      ],
    };

    const { result } = renderHook(() => useD3Chart(configWithZeros));
    
    const stackedBarData = result.current.stackedBarData;
    
    // First vessel should only have Status B segment
    expect(stackedBarData[0].segments).toHaveLength(1);
    expect(stackedBarData[0].segments[0].key).toBe('Status B');
    
    // Second vessel should only have Status A segment
    expect(stackedBarData[1].segments).toHaveLength(1);
    expect(stackedBarData[1].segments[0].key).toBe('Status A');
  });

  test('handles empty vessels array', () => {
    const emptyConfig = {
      ...mockConfig,
      vessels: [],
    };

    const { result } = renderHook(() => useD3Chart(emptyConfig));
    
    expect(result.current.chartHeight).toBe(0);
    expect(result.current.stackedBarData).toHaveLength(0);
  });

  test('color scales work correctly', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    expect(result.current.barColorScale('Status A')).toBe('#ff0000');
    expect(result.current.barColorScale('Status B')).toBe('#00ff00');
    expect(result.current.textColorScale('any')).toBe('#ffffff');
  });

  test('y scale positions vessels correctly', () => {
    const { result } = renderHook(() => useD3Chart(mockConfig));
    
    expect(result.current.yScale('Vessel A_0')).toBe(0);   // First vessel
    expect(result.current.yScale('Vessel B_1')).toBe(40);  // Second vessel
    expect(result.current.yScale('Vessel A_2')).toBe(80);  // Third vessel
  });
});
