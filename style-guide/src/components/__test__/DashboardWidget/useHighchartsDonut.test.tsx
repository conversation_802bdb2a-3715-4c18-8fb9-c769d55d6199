import { renderHook } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock Highcharts to avoid execution issues
jest.mock('highcharts', () => ({
  chart: jest.fn(() => ({
    destroy: jest.fn(),
    update: jest.fn(),
    reflow: jest.fn(),
  })),
  setOptions: jest.fn(),
}));

// Mock the actual hook to return predictable values
jest.mock('../../DashboardWidget/hooks/useHighchartsDonut', () => ({
  useHighchartsDonut: (data: any[], colors: string[], title: string) => ({
    chartOptions: {
      chart: {
        type: 'pie',
        height: 400,
      },
      title: {
        text: title,
      },
      series: [{
        name: 'Vessels',
        data: data.map((item, index) => ({
          name: item.name,
          y: item.y,
          color: colors[index % colors.length],
        })),
      }],
      colors: colors,
      plotOptions: {
        pie: {
          innerSize: '50%',
          dataLabels: {
            enabled: true,
          },
        },
      },
      legend: {
        enabled: true,
      },
      tooltip: {
        enabled: true,
      },
    },
    updateChart: jest.fn(),
    destroyChart: jest.fn(),
  }),
}));

const { useHighchartsDonut } = require('../../DashboardWidget/hooks/useHighchartsDonut');

const mockData = [
  { name: 'Active', y: 10 },
  { name: 'Inactive', y: 5 },
  { name: 'Maintenance', y: 3 },
];

const mockColors = ['#ff0000', '#00ff00', '#0000ff'];
const mockTitle = 'Vessel Status Distribution';

describe('useHighchartsDonut Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('returns chart options with correct structure', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    expect(result.current.chartOptions).toBeDefined();
    expect(result.current.chartOptions.chart.type).toBe('pie');
    expect(result.current.chartOptions.title.text).toBe(mockTitle);
  });

  test('processes data correctly', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    const seriesData = result.current.chartOptions.series[0].data;
    
    expect(seriesData).toHaveLength(3);
    expect(seriesData[0].name).toBe('Active');
    expect(seriesData[0].y).toBe(10);
    expect(seriesData[1].name).toBe('Inactive');
    expect(seriesData[1].y).toBe(5);
    expect(seriesData[2].name).toBe('Maintenance');
    expect(seriesData[2].y).toBe(3);
  });

  test('applies colors correctly', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    const seriesData = result.current.chartOptions.series[0].data;
    
    expect(seriesData[0].color).toBe('#ff0000');
    expect(seriesData[1].color).toBe('#00ff00');
    expect(seriesData[2].color).toBe('#0000ff');
  });

  test('handles empty data array', () => {
    const { result } = renderHook(() => useHighchartsDonut([], mockColors, mockTitle));
    
    const seriesData = result.current.chartOptions.series[0].data;
    expect(seriesData).toHaveLength(0);
  });

  test('handles more data than colors', () => {
    const extendedData = [
      ...mockData,
      { name: 'Decommissioned', y: 2 },
      { name: 'Under Construction', y: 1 },
    ];
    
    const { result } = renderHook(() => useHighchartsDonut(extendedData, mockColors, mockTitle));
    
    const seriesData = result.current.chartOptions.series[0].data;
    
    expect(seriesData).toHaveLength(5);
    // Colors should cycle
    expect(seriesData[3].color).toBe('#ff0000'); // cycles back to first color
    expect(seriesData[4].color).toBe('#00ff00'); // cycles to second color
  });

  test('configures donut chart correctly', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    const plotOptions = result.current.chartOptions.plotOptions.pie;
    
    expect(plotOptions.innerSize).toBe('50%'); // Makes it a donut
    expect(plotOptions.dataLabels.enabled).toBe(true);
  });

  test('enables legend and tooltip', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    expect(result.current.chartOptions.legend.enabled).toBe(true);
    expect(result.current.chartOptions.tooltip.enabled).toBe(true);
  });

  test('provides update and destroy functions', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    expect(result.current.updateChart).toBeDefined();
    expect(result.current.destroyChart).toBeDefined();
    expect(typeof result.current.updateChart).toBe('function');
    expect(typeof result.current.destroyChart).toBe('function');
  });

  test('handles data updates', () => {
    const { result, rerender } = renderHook(
      ({ data, colors, title }) => useHighchartsDonut(data, colors, title),
      {
        initialProps: {
          data: mockData,
          colors: mockColors,
          title: mockTitle,
        },
      }
    );
    
    const newData = [
      { name: 'Active', y: 15 },
      { name: 'Inactive', y: 8 },
    ];
    
    rerender({
      data: newData,
      colors: mockColors,
      title: mockTitle,
    });
    
    const seriesData = result.current.chartOptions.series[0].data;
    expect(seriesData).toHaveLength(2);
    expect(seriesData[0].y).toBe(15);
    expect(seriesData[1].y).toBe(8);
  });

  test('handles title updates', () => {
    const { result, rerender } = renderHook(
      ({ data, colors, title }) => useHighchartsDonut(data, colors, title),
      {
        initialProps: {
          data: mockData,
          colors: mockColors,
          title: mockTitle,
        },
      }
    );
    
    const newTitle = 'Updated Chart Title';
    
    rerender({
      data: mockData,
      colors: mockColors,
      title: newTitle,
    });
    
    expect(result.current.chartOptions.title.text).toBe(newTitle);
  });

  test('handles color updates', () => {
    const { result, rerender } = renderHook(
      ({ data, colors, title }) => useHighchartsDonut(data, colors, title),
      {
        initialProps: {
          data: mockData,
          colors: mockColors,
          title: mockTitle,
        },
      }
    );
    
    const newColors = ['#purple', '#orange', '#pink'];
    
    rerender({
      data: mockData,
      colors: newColors,
      title: mockTitle,
    });
    
    const seriesData = result.current.chartOptions.series[0].data;
    expect(seriesData[0].color).toBe('#purple');
    expect(seriesData[1].color).toBe('#orange');
    expect(seriesData[2].color).toBe('#pink');
  });

  test('handles zero values in data', () => {
    const dataWithZeros = [
      { name: 'Active', y: 10 },
      { name: 'Inactive', y: 0 },
      { name: 'Maintenance', y: 5 },
    ];
    
    const { result } = renderHook(() => useHighchartsDonut(dataWithZeros, mockColors, mockTitle));
    
    const seriesData = result.current.chartOptions.series[0].data;
    expect(seriesData).toHaveLength(3);
    expect(seriesData[1].y).toBe(0);
  });

  test('maintains chart configuration consistency', () => {
    const { result } = renderHook(() => useHighchartsDonut(mockData, mockColors, mockTitle));
    
    const options = result.current.chartOptions;
    
    // Check all required configuration is present
    expect(options.chart).toBeDefined();
    expect(options.title).toBeDefined();
    expect(options.series).toBeDefined();
    expect(options.plotOptions).toBeDefined();
    expect(options.legend).toBeDefined();
    expect(options.tooltip).toBeDefined();
  });
});
