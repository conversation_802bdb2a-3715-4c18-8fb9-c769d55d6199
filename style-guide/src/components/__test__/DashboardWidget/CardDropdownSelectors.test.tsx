import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CardDropdownSelectors } from '../../DashboardWidget/iCard/CardDropdownSelectors';

// Mock the CardDropdownSelectGroup component
jest.mock('../../DashboardWidget/iCard/CardSelectGroup', () => ({
  CardDropdownSelectGroup: function MockCardDropdownSelectGroup(props: any) {
    return (
      <div data-testid="card-dropdown-select-group">
        <span data-testid="dropdown-placeholder">{props.config.placeholder}</span>
        <span data-testid="selected-items">{props.selectedItems.join(',')}</span>
      </div>
    );
  },
}));

const mockProps = {
  multiSelects: [
    {
      placeholder: 'Select Status',
      width: '200px',
      groups: [
        { label: 'Status', options: ['Active', 'Inactive'] }
      ],
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
    {
      placeholder: 'Select Type',
      width: '180px',
      groups: [
        { label: 'Type', options: ['Cargo', 'Tanker', 'Container'] }
      ],
      isSearchBoxVisible: false,
      isSelectAllVisible: false,
    },
  ],
  selectStates: [['Active'], ['Cargo']],
  onSelectChange: jest.fn(),
};

describe('CardDropdownSelectors Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(<CardDropdownSelectors {...mockProps} />);
    expect(screen.getAllByTestId('card-dropdown-select-group')[0]).toBeInTheDocument();
  });

  test('renders all dropdown selectors', () => {
    render(<CardDropdownSelectors {...mockProps} />);

    const dropdowns = screen.getAllByTestId('card-dropdown-select-group');
    expect(dropdowns).toHaveLength(2);
  });

  test('displays correct placeholders for dropdowns', () => {
    render(<CardDropdownSelectors {...mockProps} />);

    expect(screen.getByText('Select Status')).toBeInTheDocument();
    expect(screen.getByText('Select Type')).toBeInTheDocument();
  });

  test('displays correct selected values', () => {
    render(<CardDropdownSelectors {...mockProps} />);

    const selectedItems = screen.getAllByTestId('selected-items');
    expect(selectedItems[0]).toHaveTextContent('Active');
    expect(selectedItems[1]).toHaveTextContent('Cargo');
  });

  test('applies correct container class', () => {
    const { container } = render(<CardDropdownSelectors {...mockProps} />);

    const selectorsContainer = container.querySelector('.ra-vessel-selects-container');
    expect(selectorsContainer).toBeInTheDocument();
  });

  test('renders with empty multiSelects array', () => {
    const propsWithEmptySelects = {
      ...mockProps,
      multiSelects: [],
    };

    render(<CardDropdownSelectors {...propsWithEmptySelects} />);

    const dropdowns = screen.queryAllByTestId('card-dropdown-select-group');
    expect(dropdowns).toHaveLength(0);
  });

  test('handles null multiSelects', () => {
    const propsWithNullSelects = {
      ...mockProps,
      multiSelects: null as any,
    };

    const { container } = render(<CardDropdownSelectors {...propsWithNullSelects} />);
    expect(container.firstChild).toBeNull();
  });

  test('renders with single dropdown selector', () => {
    const propsWithSingleSelect = {
      ...mockProps,
      multiSelects: [
        {
          placeholder: 'Single Status',
          width: '200px',
          groups: [{ label: 'Status', options: ['Active', 'Inactive'] }],
          isSearchBoxVisible: true,
          isSelectAllVisible: true,
        },
      ],
      selectStates: [['Active']],
    };

    render(<CardDropdownSelectors {...propsWithSingleSelect} />);

    const dropdowns = screen.getAllByTestId('card-dropdown-select-group');
    expect(dropdowns).toHaveLength(1);
    expect(screen.getByText('Single Status')).toBeInTheDocument();
  });

  test('passes correct props to CardDropdownSelectGroup components', () => {
    render(<CardDropdownSelectors {...mockProps} />);

    // Check that props are passed correctly
    const placeholders = screen.getAllByTestId('dropdown-placeholder');
    const selectedItems = screen.getAllByTestId('selected-items');

    expect(placeholders[0]).toHaveTextContent('Select Status');
    expect(selectedItems[0]).toHaveTextContent('Active');

    expect(placeholders[1]).toHaveTextContent('Select Type');
    expect(selectedItems[1]).toHaveTextContent('Cargo');
  });

  test('handles selects with missing groups', () => {
    const propsWithMissingGroups = {
      ...mockProps,
      multiSelects: [
        {
          placeholder: 'No Groups',
          width: '200px',
          // groups is missing
          isSearchBoxVisible: true,
          isSelectAllVisible: true,
        },
        {
          placeholder: 'With Groups',
          width: '200px',
          groups: [{ label: 'Status', options: ['Active', 'Inactive'] }],
          isSearchBoxVisible: true,
          isSelectAllVisible: true,
        },
      ],
      selectStates: [[], ['Active']],
    };

    render(<CardDropdownSelectors {...propsWithMissingGroups} />);

    // Should only render the one with groups
    const dropdowns = screen.getAllByTestId('card-dropdown-select-group');
    expect(dropdowns).toHaveLength(1);
    expect(screen.getByText('With Groups')).toBeInTheDocument();
  });

  test('maintains dropdown order', () => {
    render(<CardDropdownSelectors {...mockProps} />);

    const placeholders = screen.getAllByTestId('dropdown-placeholder');
    expect(placeholders[0]).toHaveTextContent('Select Status');
    expect(placeholders[1]).toHaveTextContent('Select Type');
  });

  test('handles empty selectStates', () => {
    const propsWithEmptyStates = {
      ...mockProps,
      selectStates: [],
    };

    render(<CardDropdownSelectors {...propsWithEmptyStates} />);

    const selectedItems = screen.getAllByTestId('selected-items');
    expect(selectedItems[0]).toHaveTextContent(''); // Empty selection
    expect(selectedItems[1]).toHaveTextContent(''); // Empty selection
  });

  test('applies correct CSS structure', () => {
    const { container } = render(<CardDropdownSelectors {...mockProps} />);

    const selectorsContainer = container.querySelector('.ra-vessel-selects-container');
    expect(selectorsContainer).toBeInTheDocument();

    const dropdowns = container.querySelectorAll('[data-testid="card-dropdown-select-group"]');
    expect(dropdowns).toHaveLength(2);
  });
});
