// hooks/useMediaQuery.ts
import { useState, useEffect } from 'react';

export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
} as const;

type Breakpoint = keyof typeof breakpoints;
type Direction = 'min' | 'max';

export function useMediaQuery(
  breakpoint: Breakpoint,
  direction: Direction = 'min',
): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (
      typeof window === 'undefined' ||
      typeof window.matchMedia !== 'function'
    ) {
      return;
    }

    const value = breakpoints[breakpoint];
    const adjustedValue = direction === 'max' ? value - 0.02 : value;
    const query = `(${direction}-width: ${adjustedValue}px)`;

    const mediaQueryList = window.matchMedia(query);

    const updateMatch = () => setMatches(mediaQueryList.matches);
    updateMatch();

    mediaQueryList.addEventListener('change', updateMatch);
    return () => mediaQueryList.removeEventListener('change', updateMatch);
  }, [breakpoint, direction]);

  return matches;
}
