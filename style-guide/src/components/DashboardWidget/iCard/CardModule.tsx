import React, { useMemo, useState, useCallback } from 'react';
import { RotateCw } from 'lucide-react';
import classNames from 'classnames';
import {
  Vessel,
  MultiSelectConfig,
  GridComponentType,
  ChartData,
} from '../types/card-types';
import CardList from './CardList';
import CardGrid from './CardGrid';
import { CardModuleHeader } from './CardModuleHeader';
import { CardDropdownSelectors } from './CardDropdownSelectors';
import { ModuleModal } from './ModuleModal';
import { CardTabs } from './CardTabs';
import './styles/CardContainer.scss';
import { ColumnDef } from '@tanstack/react-table';
import { useMediaQuery } from '../hooks/useMediaQuery';
export interface CardModuleProps {
  readonly title: string;
  readonly vessels: Vessel[];
  readonly multiVesselSelects: MultiSelectConfig[];

  readonly staticData: {
    readonly tabs: string[];
    readonly tableHeaders: string[];
    readonly badgeColors: string[];
    readonly severityData?: any; //it may change
    barChartMaxRange: number;
    readonly configKey?: string;
  };

  // Callbacks
  readonly onRefresh: () => void;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly fetchNextPage: () => void;
  readonly onChangeActiveTab: (activeTab: string) => void;

  readonly sizeKey: 'sm' | 'md' | 'lg';

  // State flags
  readonly visibleConfig: {
    readonly IsiconRenderVisible?: boolean;
    readonly IsenLargeIconVisible?: boolean;
    readonly IsVesselSelectVisible?: boolean;
    readonly IsAlltabsVisible?: boolean;
    readonly IsAllTabVisible?: boolean;
    readonly IsLastUpdatedVisible: boolean;
    readonly IsRefereshIconVisible: boolean;
    readonly vesselSelectPosition?: 'before' | 'after';
    readonly filterApplyonRenderData: string;
  };
  columns: ColumnDef<Vessel>[];
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;
  readonly pagination: any; // Using `any` for simplification as the exact type is complex

  readonly componentView: {
    readonly gridComponent?: GridComponentType;
    readonly defaultComponent?: 'list' | 'grid';
  };
  readonly responsive: boolean;
  readonly responsiveConfig: {
    designName: string;
    component: React.ComponentType<{ data: Vessel }>;
  };
  responsiveCardContainerHeight?: string;
  responsiveCardListContainerHeight?: string;
}

export const findMaxBarChartValue = (data: any[]): number => {
  if (!data || data.length === 0) {
    return 0;
  }

  const values = data.map((item) => item.countforBarChart);
  return Math.max(...values);
};

export default function CardModule({
  title,
  vessels,
  staticData,
  visibleConfig,
  multiVesselSelects = [],
  componentView,
  sizeKey = 'md',
  onRefresh,
  onSendEmail,
  onVesselClick,
  fetchNextPage,
  onChangeActiveTab,
  isFetchingNextPage,
  isLoading,
  pagination,
  columns: columnsProp,
  responsive,
  responsiveConfig,
  responsiveCardContainerHeight,
  responsiveCardListContainerHeight,
}: Readonly<CardModuleProps>) {
  // Destructure configKey outside of useMemo
  const { configKey, severityData } = staticData;
  const isMobileOrTablet = useMediaQuery("lg", "max");
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    componentView?.defaultComponent || "list"
  );
  const [activeTab, setActiveTab] = useState<string>(() => {
    if (visibleConfig?.IsAllTabVisible) {
      return 'All'; // default to All tab when visible
    }
    return staticData.tabs[0] || ''; // default to first real tab
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => []),
  );

  const filterKeyMapping = {
    vessel_id: { dropdownKey: 'vessel_id', renderKey: 'vessel_id' },
    vessel_code: {
      dropdownKey: 'vessel_account_code_new',
      renderKey: 'vessel_code',
    },
  };

  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab); // Update the local state
      onChangeActiveTab(tab); // Call the parent's callback
    },
    [onChangeActiveTab],
  );

  const { filteredVessels, chartData } = useMemo(() => {
    const defaultChartData = {
      openDeficiencies: {
        title: 'Open Deficiencies (Not accepted by office)',
        total: 0,
        data: [
          { label: 'Overdue', value: 0, color: '#d80e61' },
          { label: 'Due within 30 days', value: 0, color: '#fbc02d' },
          { label: 'Others', value: 0, color: '#27a527' },
        ],
      },
      closedDeficiencies: {
        title: 'Closed Deficiencies (Accepted by office)',
        total: 0,
        data: [
          { label: 'High', value: 0, color: '#d80e61' },
          { label: 'Medium', value: 0, color: '#fbc02d' },
          { label: 'Low', value: 0, color: '#27a527' },
        ],
      },
    };

    if (!vessels) {
      return { filteredVessels: [], chartData: defaultChartData };
    }

    let filteredData = [...vessels];

    // Filter by Active Tab
    if (activeTab !== 'All') {
      filteredData = filteredData.filter((v: Vessel) => v.type === activeTab);
    } else {
      // New logic for 'surveys-certificates' configKey
      if (configKey === 'surveys-certificates') {
        const groupedVessels = new Map<string, Vessel>();

        vessels.forEach((vessel) => {
          // Create a unique key for grouping
          const groupKey = `${vessel.vessel_id}`;

          if (groupedVessels.has(groupKey)) {
            // If a vessel with the same IDs exists, combine their data
            const existingVessel = groupedVessels.get(groupKey)!;

            if (
              Array.isArray(existingVessel.vesselData) &&
              Array.isArray(vessel.vesselData)
            ) {
              existingVessel.vesselData.forEach((item, index) => {
                existingVessel.vesselData[index] += vessel.vesselData[index];
              });
            }

            // 1. Update countforBarChart
            existingVessel.countforBarChart =
              (existingVessel.countforBarChart || 0) +
              (vessel.countforBarChart || 0);

            // 2. Update overdue, due_within_30_days, and due_within_60_days
            existingVessel.overdue =
              (existingVessel.overdue || 0) + (vessel.overdue || 0);
            existingVessel.due_within_30_days =
              (existingVessel.due_within_30_days || 0) +
              (vessel.due_within_30_days || 0);
            existingVessel.due_within_60_days =
              (existingVessel.due_within_60_days || 0) +
              (vessel.due_within_60_days || 0);
          } else {
            // If it's a new group, add the vessel to the map
            groupedVessels.set(groupKey, {
              ...vessel,
              vesselData: [...(vessel.vesselData || [])],
              // Initialize counts for the new group
              countforBarChart: vessel.countforBarChart || 0,
              overdue: vessel.overdue || 0,
              due_within_30_days: vessel.due_within_30_days || 0,
              due_within_60_days: vessel.due_within_60_days || 0,
            });
          }
        });

        // Convert the Map back to an array
        filteredData = Array.from(groupedVessels.values());
      }

      if (configKey === 'deficiencies') {
        const groupedVessels = new Map<string, Vessel>();

        vessels.forEach((vessel) => {
          // Create a unique key for grouping
          const groupKey = `${vessel.vessel_id}`;

          if (groupedVessels.has(groupKey)) {
            const existingVessel = groupedVessels.get(groupKey)!;

            // Sum up the values from the current vessel to the existing grouped vessel
            existingVessel.accepted_by_office =
              (existingVessel.accepted_by_office || 0) +
              (vessel.accepted_by_office || 0);
            existingVessel.not_accepted_by_office =
              (existingVessel.not_accepted_by_office || 0) +
              (vessel.not_accepted_by_office || 0);
            existingVessel.overdue =
              (existingVessel.overdue || 0) + (vessel.overdue || 0);
            existingVessel.due_within_30_days =
              (existingVessel.due_within_30_days || 0) +
              (vessel.due_within_30_days || 0);
            existingVessel.others =
              (existingVessel.others || 0) + (vessel.others || 0);

            // Sum up the severity values
            // if (vessel.severity && existingVessel.severity) {
            //   existingVessel.severity.low = vessel.severity.low || 0;
            //   existingVessel.severity.medium = vessel.severity.medium || 0;
            //   existingVessel.severity.high = vessel.severity.high || 0;
            // }
          } else {
            // If it's a new vessel, add it to the map
            groupedVessels.set(groupKey, { ...vessel });
          }
        });

        // Convert the Map back to an array
        filteredData = Array.from(groupedVessels.values());
      }
    }

    // Filter 1: Vessel Name
    const vesselNameSelections = selectStates[0];
    if (vesselNameSelections?.length > 0) {
      const allVesselOptions =
        multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];

      // Use the mapping to get the correct keys
      const keys =
        filterKeyMapping[visibleConfig.filterApplyonRenderData] ||
        filterKeyMapping.vessel_id;

      //selectedVesselIdentifiers set of  vessel_id's of all selecte active vessels names from the first select box
      const selectedVesselIdentifiers = new Set(
        allVesselOptions
          .filter((opt) => vesselNameSelections.includes(opt.name))
          .map((opt) => `${opt[keys.dropdownKey]}`),
      );

      //the filteredData array contains objects with vessel.id, so filter out them to render on table or grid view
      if (selectedVesselIdentifiers.size > 0) {
        filteredData = filteredData.filter((vessel) =>
          selectedVesselIdentifiers.has(`${vessel[keys.renderKey]}`),
        );
      }
    }

    // Filter 2: Level RA
    const levelRaSelections = selectStates[1];
    if (levelRaSelections?.length > 0) {
      filteredData = filteredData.filter((vessel: Vessel) => {
        return levelRaSelections.includes(vessel.ra_level);
      });
    }

    let newChartData = defaultChartData;
    if (configKey === 'deficiencies') {
      // Now, calculate chart data from the filtered data
      const totalOverdue = filteredData.reduce(
        (sum: number, item: any) => sum + (item.overdue || 0),
        0,
      );
      const totalDueWithin30Days = filteredData.reduce(
        (sum: number, item: any) => sum + (item.due_within_30_days || 0),
        0,
      );
      const totalOthers = filteredData.reduce(
        (sum: number, item: any) => sum + (item.others || 0),
        0,
      );
      const totalOpenDeficiencies =
        totalOverdue + totalDueWithin30Days + totalOthers;

      const closedDeficiencyTotals = {
        high: 0,
        medium: 0,
        low: 0,
      };

      if (severityData) {
        if (activeTab === "All") {
          // For "All" tab, sum up all severity values
          for (const category in severityData) {
            if (severityData.hasOwnProperty(category)) {
              const severity = severityData[category];
              closedDeficiencyTotals.high += severity.high || 0;
              closedDeficiencyTotals.medium += severity.medium || 0;
              closedDeficiencyTotals.low += severity.low || 0;
            }
          }
        } else {
          // For a specific tab, use the severity data for that category
          const severity = severityData[activeTab] || {
            high: 0,
            medium: 0,
            low: 0,
          };
          closedDeficiencyTotals.high = severity.high || 0;
          closedDeficiencyTotals.medium = severity.medium || 0;
          closedDeficiencyTotals.low = severity.low || 0;
        }
      }

      const totalClosedDeficiencies = filteredData.reduce(
        (sum: number, item: any) => sum + (item.accepted_by_office || 0),
        0,
      );

      // update chart data
      newChartData = {
        openDeficiencies: {
          title: 'Open Deficiencies (Not accepted by office)',
          total: totalOpenDeficiencies,
          data: [
            { label: 'Overdue', value: totalOverdue, color: '#d80e61' },
            {
              label: 'Due within 30 days',
              value: totalDueWithin30Days,
              color: '#fbc02d',
            },
            { label: 'Others', value: totalOthers, color: '#27a527' },
          ],
        },
        closedDeficiencies: {
          title: 'Closed Deficiencies (Accepted by office)',
          total: totalClosedDeficiencies,
          data: [
            {
              label: 'High',
              value: closedDeficiencyTotals.high,
              color: '#d80e61',
            },
            {
              label: 'Medium',
              value: closedDeficiencyTotals.medium,
              color: '#fbc02d',
            },
            {
              label: 'Low',
              value: closedDeficiencyTotals.low,
              color: '#27a527',
            },
          ],
        },
      };
    }

    return { filteredVessels: filteredData, chartData: newChartData };
  }, [
    vessels,
    activeTab,
    selectStates,
    visibleConfig.filterApplyonRenderData,
    multiVesselSelects,
    configKey,
    staticData.severityData,
  ]);

  // Use a local variable to calculate maxRange and pass it to CardGrid
  const barChartMaxRange = findMaxBarChartValue(filteredVessels);

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback(
    (index: number, newSelected: string[]) => {
      setSelectStates((prevStates) => {
        const newStates = [...prevStates];
        newStates[index] = newSelected;
        return newStates;
      });
    },
    [],
  );

  const hasVesselsData = vessels && vessels.length > 0;
  const finalIsiconRenderVisible = hasVesselsData
    ? visibleConfig.IsiconRenderVisible
    : false;
  const finalIsenLargeIconVisible = hasVesselsData
    ? visibleConfig.IsenLargeIconVisible
    : false;

  const renderViewContent = () =>
    viewMode === 'list' ? (
      <CardList
        vessels={filteredVessels}
        columns={columnsProp} // Pass the columns array here
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage ?? false}
        isLoading={isLoading ?? false}
        fetchNextPage={fetchNextPage}
        responsive={responsive}
        responsiveConfig={responsiveConfig}
        responsiveCardListContainerHeight={responsiveCardListContainerHeight}
        isMobileOrTablet={isMobileOrTablet}
      />
    ) : (
      <CardGrid
        vessels={filteredVessels}
        tableHeaders={staticData.tableHeaders}
        badgeColors={staticData.badgeColors}
        chartData={chartData}
        barChartMaxRange={barChartMaxRange}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        gridComponent={componentView?.gridComponent}
        configKey={configKey}
        activeTab={activeTab}
      />
    );

  const renderModuleCore = () => (
    <>
      <CardModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModalOpen}
        IsiconRenderVisible={finalIsiconRenderVisible}
        IsenLargeIconVisible={finalIsenLargeIconVisible}
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      {visibleConfig.IsLastUpdatedVisible && (
        <div className='ra-last-updated-container'>
          <p className='ra-last-updated-text'>
            Last Updated on:{' '}
            {`${lastUpdated.toLocaleDateString(undefined, {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })} ${lastUpdated.toLocaleTimeString(undefined, {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })}`}
            {visibleConfig.IsRefereshIconVisible && (
              <RotateCw onClick={handleRefresh} className='ra-refresh-icon' />
            )}
          </p>
        </div>
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === 'before' &&
        viewMode === 'list' && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {visibleConfig.IsAlltabsVisible && (
        <CardTabs
          tabs={staticData.tabs}
          activeTab={activeTab}
          IsAlltabsVisible={visibleConfig.IsAlltabsVisible}
          IsAllTabVisible={visibleConfig.IsAllTabVisible}
          onTabChange={handleTabChange}
        />
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === 'after' &&
        viewMode === 'list' && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
          />
        )}

      {renderViewContent()}
    </>
  );

  return (
    <>
      <div
        style={
          isMobileOrTablet && responsive && responsiveCardContainerHeight
            ? { minHeight: responsiveCardContainerHeight }
            : {}
        }
        className={classNames('ra-vessel-card-container', `size-${sizeKey}`)}
      >
        {renderModuleCore()}
      </div>

      {isModalOpen && (
        <ModuleModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          sizeKey={sizeKey}
        >
          {renderModuleCore()}
        </ModuleModal>
      )}
    </>
  );
}
