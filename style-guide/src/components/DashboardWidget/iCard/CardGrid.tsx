import React from "react";
import <PERSON><PERSON>hart from "../common/BarChart";
import { DashboardGrid } from "../common/DashboardGrid";
import { CardGridProps } from "../types/card-types";
import "./styles/CardGrid.scss";

// need to remove all inline css from it's all child component's hooks as well
export default function CardGrid({
  vessels,
  tableHeaders,
  badgeColors,
  chartData,
  barChartMaxRange = 250,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  gridComponent = "bar",
  configKey,
  activeTab,
  ...rest
}: Readonly<CardGridProps>) {
  // Use infinite scroll only for the 'list' or 'bar' view, not for 'pie'
  const isScrollable = gridComponent !== 'pie';
  const hasNextPage = !!pagination && pagination.page < pagination.totalPages;

  const renderContent = () => {
    if (gridComponent === "pie") {
      if (chartData) {
        return <DashboardGrid chartData={chartData} />;
      }
      return <div className="no-results-cell">Chart data not found.</div>;
    }

    // Default to Bar Chart
    const safeVessels = Array.isArray(vessels) ? vessels : [];
    const barChartData = safeVessels.map((vessel) => {
      const values: Record<string, number | string> = { name: vessel.name, vessel_ownership_id: vessel.vessel_ownership_id || 0 };
      (tableHeaders || [])
        .filter((h) => h !== 'Vessel' && h !== 'Action')
        .forEach((header, idx) => {
          values[header] = (vessel.vesselData ?? [])[idx] as any;
        });
      return values;
    });

    if (safeVessels.length === 0) {
      return <div className="no-results-cell">No results found</div>;
    }

    // need to add a edge case for range of total vesselbar  count should not maximize the range limit here
    return (
      <div className="ra-vessel-bar-chart-wrapper">
        <BarChart
          vessels={barChartData}
          valueHeaders={tableHeaders.filter(
            (h) => h !== "Vessel" && h !== "Action"
          )}
          badgeColors={badgeColors}
          valueDomain={[0, barChartMaxRange]}
          configKey={configKey}
          activeTab={activeTab}
          isModal={true}
          {...rest}
        />
      </div>
    );
  };

  return <div className="ra-vessel-grid-root">{renderContent()}</div>;
}
